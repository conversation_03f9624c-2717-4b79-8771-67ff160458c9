# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Language
chinese

## Project Overview

DeepChat is a feature-rich open-source AI chat platform built with Electron + Vue 3 + TypeScript. It supports multiple cloud and local LLM providers, advanced MCP (Model Context Protocol) tool calling, and multi-window/multi-tab architecture.

## Development Commands

### Package Management

Use `pnpm` as the package manager (required Node.js >= 20.12.2, pnpm >= 10.11.0):

```bash
# Install dependencies
pnpm install

# Install runtime dependencies for MCP and Python execution
pnpm run installRuntime
```

### Development

```bash
# Start development server
pnpm run dev

# Start development with inspector for debugging
pnpm run dev:inspect

# Linux development (disable sandbox)
pnpm run dev:linux
```

### Code Quality

```bash
# Lint with OxLint
pnpm run lint

# Format code with Prettier
pnpm run format

# Type checking
pnpm run typecheck
# or separately:
pnpm run typecheck:node  # Main process
pnpm run typecheck:web   # Renderer process
```

### Testing

```bash
# Run all tests
pnpm run test

# Run tests with coverage
pnpm run test:coverage

# Run tests in watch mode
pnpm run test:watch

# Run tests with UI
pnpm run test:ui

# Run specific test suites
pnpm run test:main      # Main process tests
pnpm run test:renderer  # Renderer process tests
```

### Building

```bash
# Build for development preview
pnpm run build

# Build for production (platform-specific)
pnpm run build:win      # Windows
pnpm run build:mac      # macOS
pnpm run build:linux    # Linux

# Build for specific architectures
pnpm run build:win:x64
pnpm run build:win:arm64
pnpm run build:mac:x64
pnpm run build:mac:arm64
pnpm run build:linux:x64
pnpm run build:linux:arm64
```

### Internationalization

```bash
# Check i18n completeness (Chinese as source)
pnpm run i18n

# Check i18n completeness (English as source)
pnpm run i18n:en
```

## Architecture Overview

### Multi-Process Architecture

- **Main Process**: Core business logic, system integration, window management, MCP server lifecycle
- **Renderer Process**: UI components, user interactions, frontend state management
- **Preload Scripts**: Secure IPC bridge between main and renderer processes
- **Independent Processes**: Shell (tab management) and Content (application) run in separate renderer processes

### Key Architectural Patterns

#### Presenter Pattern

Each functional domain has a dedicated Presenter class in `src/main/presenter/`:

- **WindowPresenter**: BrowserWindow lifecycle management, window state persistence
- **TabPresenter**: WebContentsView management with cross-window tab dragging, tab state synchronization
- **ThreadPresenter**: Conversation session management, LLM coordination, message streaming
- **McpPresenter**: MCP server connections, tool execution, format conversion between LLM providers
- **ConfigPresenter**: Unified configuration management with encryption interfaces
- **LLMProviderPresenter**: LLM provider abstraction with Agent Loop architecture for multi-turn tool calling
- **FilePresenter**: File system operations with adapter pattern for different file types
- **KnowledgePresenter**: Built-in knowledge base with DuckDB integration
- **SyncPresenter**: Data synchronization and backup management

#### Multi-Window Multi-Tab Architecture

- **Window Shell** (`src/renderer/shell/`): Lightweight tab bar UI management, independent Vue instance
- **Tab Content** (`src/renderer/src/`): Complete application functionality, separate Vue instance per tab
- **Cross-Window Tab Dragging**: Seamless tab movement between windows with state preservation
- **Independent Processes**: Each window and tab runs in isolated processes for stability

#### Event-Driven Communication

- **EventBus** (`src/main/eventbus.ts`): Centralized event coordination system with typed events
- **Standard Event Patterns**: Namespaced events (`config:`, `conversation:`, `sync:`, `update:`) with clear responsibility separation
- **IPC Integration**: EventBus bridges main process events to renderer via `mainWindow.webContents.send()`
- **Renderer to Main**: Direct presenter method calls via `usePresenter.ts` composable

### LLM Provider Architecture

The LLM system follows a sophisticated two-layer architecture:

1. **Agent Loop Layer** (`llmProviderPresenter/index.ts`):
   - Manages conversation flow with multi-turn tool calling and state tracking
   - Coordinates tool execution via McpPresenter with permission checking
   - Standardizes streaming events sent to frontend with error recovery
   - Handles tool call batching and parallel execution

2. **Provider Layer** (`llmProviderPresenter/providers/*.ts`):
   - 30+ provider implementations (OpenAI, Anthropic, Gemini, Ollama, etc.)
   - Converts MCP tools to provider-specific formats (OpenAI tools, Anthropic tool use, Gemini function calling)
   - Normalizes streaming responses to standard event interface
   - Supports both native tool calling and prompt-wrapped fallbacks
   - Handles provider-specific authentication and rate limiting

### MCP Integration Architecture

- **Server Management**: Lifecycle management of MCP servers with automatic npm registry optimization
- **Tool Execution**: Seamless integration with LLM providers via format conversion layer
- **Transport Support**: Stdio, SSE, HTTP, and InMemory transport protocols
- **Built-in Services**: 15+ in-memory servers for code execution, web search, file operations, meeting analysis
- **Permission System**: Granular tool permission management with user prompts
- **Tool Conflict Resolution**: Automatic name deduplication and namespacing

### Data Persistence Architecture

- **SQLite Database**: Structured data storage for conversations, messages, attachments
- **Electron Store**: Configuration and user settings with encryption support
- **File System**: User file storage with type-specific adapters (PDF, DOCX, CSV, images, audio)
- **DuckDB Integration**: High-performance vector storage for built-in knowledge base

## Code Structure

### Main Process (`src/main/`)

- `presenter/`: Core business logic organized by functional domain
- `eventbus.ts`: Central event coordination system
- `index.ts`: Application entry point and lifecycle management

### Renderer Process (`src/renderer/`)

- `src/`: Main application UI (Vue 3 + Composition API)
- `shell/`: Tab management UI shell
- `floating/`: Floating button interface

### Shared Code (`src/shared/`)

- Type definitions shared between main and renderer processes
- Common utilities and constants
- IPC contract definitions

## Development Guidelines

### Code Standards

- **Language**: Use English for logs and comments
- **TypeScript**: Strict type checking enabled
- **Vue 3**: Use Composition API for all components
- **State Management**: Pinia for frontend state
- **Styling**: Tailwind CSS with scoped styles

### IPC Communication

- **Renderer to Main**: Use `usePresenter.ts` composable for direct presenter method calls
- **Main to Renderer**: Use EventBus to broadcast events via `mainWindow.webContents.send()`
- **Security**: Context isolation enabled with preload scripts

### Testing

- **Framework**: Vitest for unit and integration tests
- **Test Files**: Place in `test/` directory with corresponding structure
- **Coverage**: Run tests with coverage reporting

### File Organization

- **Presenters**: One presenter per functional domain
- **Components**: Organize by feature in `src/renderer/src/`
- **Types**: Shared types in `src/shared/`
- **Configuration**: Centralized in `configPresenter/`

## Common Development Tasks

### Adding New LLM Provider

1. Create provider file in `src/main/presenter/llmProviderPresenter/providers/`
2. Implement `coreStream` method following standardized event interface
3. Add provider configuration in `configPresenter/providers.ts`
4. Update UI in renderer provider settings

### Adding New MCP Tool

1. Implement tool in `src/main/presenter/mcpPresenter/inMemoryServers/`
2. Register in `mcpPresenter/index.ts`
3. Add tool configuration UI if needed

### Creating New UI Components

1. Follow existing component patterns in `src/renderer/src/`
2. Use Composition API with proper TypeScript typing
3. Implement responsive design with Tailwind CSS
4. Add proper error handling and loading states

### Debugging

- **Main Process**: Use VSCode debugger with breakpoints
- **Renderer Process**: Chrome DevTools (F12)
- **MCP Tools**: Built-in MCP debugging window
- **Event Flow**: EventBus logging for event tracing

## Key Dependencies

### Core Framework

- **Electron**: Desktop application framework
- **Vue 3**: Progressive web framework
- **TypeScript**: Type-safe JavaScript
- **Vite**: Fast build tool via electron-vite

### State & Routing

- **Pinia**: Vue state management
- **Vue Router**: SPA routing

### UI & Styling

- **Tailwind CSS**: Utility-first CSS
- **Radix Vue**: Accessible UI components
- **Monaco Editor**: Code editor integration

### LLM Integration

- **Multiple SDK**: OpenAI, Anthropic, Google AI, etc.
- **Ollama**: Local model support
- **MCP SDK**: Model Context Protocol support

### Development Tools

- **OxLint**: Fast linting
- **Prettier**: Code formatting
- **Vitest**: Testing framework
- **Vue DevTools**: Vue debugging support

## Security Considerations

- Context isolation enabled for secure IPC
- Preload scripts provide controlled API exposure
- Configuration encryption interfaces available
- Network proxy support for privacy
- Screen capture hiding capabilities

## Performance Optimization

- Lazy loading for application startup
- Efficient event handling via EventBus
- Optimized build with tree-shaking
- Monaco Editor worker separation
- Streaming responses for real-time chat

## Platform-Specific Notes

### Windows

- Enable Developer Mode or use admin account for symlink creation
- Install Visual Studio Build Tools for native dependencies

### macOS

- Code signing configuration in `scripts/notarize.js`
- Platform-specific build configurations

### Linux

- AppImage and deb package support
- Sandbox considerations for development

## Git Commit

- Do not include author information other than human authors in the Commit, such as Co-Authored-By related information
