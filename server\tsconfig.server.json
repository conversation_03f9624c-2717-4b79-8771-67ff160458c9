{"compilerOptions": {"target": "ES2020", "module": "ES2020", "lib": ["ES2020"], "outDir": "../dist/server", "rootDir": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["../src/*"]}, "typeRoots": ["node_modules/@types"]}, "include": ["**/*"], "exclude": ["node_modules", "dist"]}