{"compilerOptions": {"target": "ES2020", "module": "ES2020", "lib": ["ES2020"], "outDir": "./dist", "rootDir": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "sourceMap": false, "removeComments": true, "moduleResolution": "node", "baseUrl": ".", "typeRoots": ["node_modules/@types"]}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist", "tests", "**/*.test.ts", "**/*.spec.ts"]}