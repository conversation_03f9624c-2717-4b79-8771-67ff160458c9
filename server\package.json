{"name": "deepchat-server", "version": "1.0.0", "description": "DeepChat Backend Server", "main": "dist/index.js", "type": "module", "scripts": {"dev": "nodemon --exec \"tsx\" index.ts", "build": "tsc", "start": "node dist/index.js", "start:prod": "NODE_ENV=production node dist/index.js"}, "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.1", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.2", "@types/multer": "^1.4.11", "@types/node": "^20.4.5", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "cross-env": "^10.0.0", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "tsx": "^4.20.5", "typescript": "^5.1.6"}}