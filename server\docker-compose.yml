version: '3.8'

services:
  # DeepChat后端服务
  deepchat-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: deepchat-server
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: ${PORT:-3000}
      # 使用.env文件中配置的MongoDB URI
      MONGODB_URI: ${MONGODB_URI}
      JWT_SECRET: ${JWT_SECRET}
      FILE_SIZE_LIMIT: ${FILE_SIZE_LIMIT:-50mb}
      UPLOAD_DIR: ${UPLOAD_DIR:-uploads}
      TEMP_DIR: ${TEMP_DIR:-temp}
      DOMAIN: ${DOMAIN}
    ports:
      - "${PORT:-3000}:${PORT:-3000}"
    volumes:
      - ./uploads:/app/uploads
      - ./temp:/app/temp
    # 使用host网络模式以访问宿主机的MongoDB
    network_mode: host
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${PORT:-3000}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
